#pragma once
#include <QObject>
#include "public/Logger.h"
#include "public/UdpSocket.h"
#include "public/ChatProtocol.h"
#include <thread>
#include <iostream>


class ChatClient:public QObject 
{
    Q_OBJECT
public:
    ChatClient(QObject* parent = nullptr)
        :QObject(parent)
        ,m_addr(SERVER_IP, SERVER_PORT)//初始网络soclet
    {
        //初始化日志
        Logger::Config config;
        Logger::GetInstance().Init(config);

    }

    ~ChatClient() {
    }

    void run(std::string name) {

        //发送登录包
        sendLogin(name);

        //进入接收消息子线程
        m_recvThread = std::thread(RecvThread,this);
        LOG_INFO("CommandThread Create RecvThread Id:%d", m_recvThread.get_id());

        //等待线程结束
        //m_recvThread.join();
        
        LOG_DEBUG("ChatClient::run Exit Thread Id:%d", m_recvThread.get_id());
    }

    void sendLogin(const std::string& name) {
        LOG_DEBUG("ChatClient::sendLogin name:%s", name.c_str());
        LoginRequest request;
        strncpy(request.name, name.c_str(), sizeof(request.name));
        sendPacket(CMD_LOGIN, &request, sizeof(request));
    }

    void sendLogout(const std::string& name) {
        LOG_DEBUG("ChatClient::sendLogout name:%s", name.c_str());
        LoginRequest request;
        strncpy(request.name, name.c_str(), sizeof(request.name));
        sendPacket(CMD_LOGOUT, &request, sizeof(request));
    }

    void sendTextMessage(const std::string& msg) {
        LOG_DEBUG("ChatClient::sendTextMessage msg:%s", msg.c_str());
        TextMessage request;
        strncpy(request.msg, msg.c_str(), sizeof(request.msg));
        sendPacket(CMD_TEXT_MSG, &request, sizeof(request));
    }

    void sendPacket(uint16_t cmd, const void* data, int len) {
        char buf[MAX_PACKET_SIZE];
        MsgHeader* header = (MsgHeader*)buf;
        header->version = MSG_PROTOCOL_VERVER_1_0;
        header->command = cmd;
        header->len = len;
        int bufSize = sizeof(MsgHeader) + len;
        memcpy(buf + sizeof(MsgHeader), data, len);
        int bytes = m_udpSocket.SendTo(m_addr, buf, bufSize);

        LOG_DEBUG("ChatClient::sendPacket %s bytes:%d version:%04x command:%d len:%d",
            m_addr.toString().c_str(), bytes, header->version, header->command, header->len);
    }


    static void RecvThread(ChatClient* thiz)
    {
        try {
            LOG_INFO("ChatClient::RecvThread Wait Message");
            char buf[MAX_PACKET_SIZE];
            SocketAddress addr;
            while (thiz->isRunning) {//判断是否结束子线程
                //接收数据
                int bytes = thiz->m_udpSocket.RecvFrom(addr, buf, sizeof(buf));
                LOG_DEBUG("ChatClient::RecvThread RecvFrom %s bytes:%d",addr.toString().c_str(), bytes);
                thiz->handlePacket(buf, bytes);
            }
        }
        catch (SocketException& e) {
            LOG_FATAL(e.what());
        }
    }

    void help() {
        std::cout << "Chat Help:" << std::endl;
        std::cout << "/login" << std::endl;
        std::cout << "/logout" << std::endl;
        std::cout << "/msg" << std::endl;
        std::cout << "/quit" << std::endl;
    }

    static void CommandThread(ChatClient* thiz)
    {
        try {
            thiz->help();
            while (true) {
                std::string input;
                std::getline(std::cin, input); // /login 123
                int index = input.find(" ");
                std::string cmd = input.substr(0, index);
                std::string name = input.substr(index + 1, input.length());
                if (cmd == "/login") {
                    thiz->sendLogin(name);
                    thiz->m_recvThread = std::thread(RecvThread, thiz);
                    LOG_INFO("CommandThread Create RecvThread Id:%d", thiz->m_recvThread.get_id());
                }
                else if (cmd == "/logout") {
                    thiz->sendLogout(name);
                    thiz->isRunning = false;
                    exit(0);
                    return;
                }
                else if (cmd == "/msg") {
                    thiz->sendTextMessage(name);
                    return;
                }
            }
        }
        catch (SocketException& e) {
            LOG_FATAL(e.what());
        }
    }

    //处理包
    void handlePacket(void* data, int len) {
        if (len < sizeof(MsgHeader)) {
            LOG_ERROR("ChatClient::handlePacket len < MsgHeader");
            return;
        }

        MsgHeader* header = (MsgHeader*)data;

        //检测协议版本
        if (header->version != MSG_PROTOCOL_VERVER_1_0) {
            LOG_ERROR("ChatClient::handlePacket Verson Error:%d", header->version);
            return;
        }

        LOG_DEBUG("ChatClient::handlePacket %s version:%04x command:%d len:%d",
            m_addr.toString().c_str(), header->version, header->command, header->len);

        switch (header->command) {
        case CMD_LOGIN_RESPONSE:
            handleLoginResponse(header, header + 1);
            break;
        }
    }

    void handleLoginResponse(MsgHeader* header, void* buf) {
        if (header->len != sizeof(LoginResponse)) {
            LOG_ERROR("ChatClient::HandleLogin len Error %d != %d", header->len, sizeof(LoginRequest));
            return;
        }

        LoginResponse* data = (LoginResponse*)buf;
        if (data->resultCode == 0) {
            //std::cout << "Login OK" << std::endl;
            //登录成功
           emit loginSuccess();

        }
        else if (data->resultCode == 1) {
            std::cout << "User Name Exist" << std::endl;
        }
    }

/**
 * 停止函数，用于停止当前运行的线程
 * 将运行状态标志设置为false，并确保接收线程正确终止
 */
    void stop() {
    // 设置运行状态标志为false，通知线程停止运行
        isRunning = false;

    // 检查接收线程是否可被join（是否仍在运行）
        if (m_recvThread.joinable()) {
        // 如果线程可join，则调用join()等待线程完全终止
            m_recvThread.join();
        }
    }
private:
    bool isRunning = true;
    UdpSocket m_udpSocket;
    SocketAddress m_addr; //存储服务器那边的地址
    std::thread m_recvThread;


public slots:

signals:
    void loginSuccess();

};
